const { fetchJsonFile } = require('./fetchJson');

async function loadData() {
  var tree = {variables: {}, tags: {}, triggers: {}, macros: {}};

  try {
    const data = await fetchJsonFile('GTM-PP52DD_v518.json');
    data.containerVersion.variable.forEach(variable => {
		
		tree.variables[variable.name] = variable;
	});
  } catch (error) {
    console.error(error.message);
  }

  console.log(tree.variables);
		
}

loadData();
