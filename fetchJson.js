const fs = require('fs').promises;
const path = require('path');

/**
 * Fetches and parses a JSON file from the root folder
 * @param {string} filename - The name of the JSON file to fetch
 * @returns {Promise<Object>} - Parsed JSON object
 */
async function fetchJsonFile(filename) {
  try {
    // Construct the full path to the file in the root directory
    const filePath = path.join(__dirname, filename);
    
    // Check if file exists
    await fs.access(filePath);
    
    // Read the file
    const fileContent = await fs.readFile(filePath, 'utf8');
    
    // Parse JSON
    const jsonData = JSON.parse(fileContent);
    
    console.log(`✅ Successfully loaded and parsed: ${filename}`);
    return jsonData;
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`❌ File not found: ${filename}`);
    } else if (error instanceof SyntaxError) {
      throw new Error(`❌ Invalid JSON format in file: ${filename}`);
    } else {
      throw new Error(`❌ Error reading file ${filename}: ${error.message}`);
    }
  }
}

/**
 * Alternative synchronous version
 * @param {string} filename - The name of the JSON file to fetch
 * @returns {Object} - Parsed JSON object
 */
function fetchJsonFileSync(filename) {
  try {
    const filePath = path.join(__dirname, filename);
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const jsonData = JSON.parse(fileContent);
    
    console.log(`✅ Successfully loaded and parsed: ${filename}`);
    return jsonData;
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`❌ File not found: ${filename}`);
    } else if (error instanceof SyntaxError) {
      throw new Error(`❌ Invalid JSON format in file: ${filename}`);
    } else {
      throw new Error(`❌ Error reading file ${filename}: ${error.message}`);
    }
  }
}

// Example usage
async function example() {
  try {
    // Fetch your GTM JSON file
    const gtmData = await fetchJsonFile('GTM-PP52DD_v518.json');
    console.log('GTM Data loaded:', Object.keys(gtmData));
    
    // You can also use the sync version
    // const gtmDataSync = fetchJsonFileSync('GTM-PP52DD_v518.json');
    
  } catch (error) {
    console.error(error.message);
  }
}

// Export the functions
module.exports = {
  fetchJsonFile,
  fetchJsonFileSync
};

// Run example if this file is executed directly
if (require.main === module) {
  example();
}
