<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8" />
  <title><PERSON><PERSON><PERSON> - <PERSON>pa</title>
  <style>
    body { margin: 0; overflow: hidden; }
    svg { width: 100vw; height: 100vh; background: #f9f9f9; cursor: grab; }
    text { font-family: sans-serif; font-size: 14px; }
    .node rect { fill: #fff; stroke: #000; rx: 5; ry: 5; }
  </style>
</head>
<body>
<svg id="mindmap"></svg>

<script>
  const data = [
    {
      text: "Strom 1",
      children: [
        { text: "A1", children: [{ text: "A1.1", children: [] }] },
        { text: "A2", children: [] }
      ]
    },
    {
      text: "Strom 2",
      children: [
        { text: "B1", children: [{ text: "B1.1", children: [] }] }
      ]
    },
    {
      text: "Strom 3",
      children: [
        { text: "C1", children: [] },
        { text: "C2", children: [{ text: "C2.1", children: [] }, { text: "C2.2", children: [] }] }
      ]
    }
  ];

  const svg = document.getElementById("mindmap");
  const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
  svg.appendChild(g);

  let nodeId = 0;
  const nodeHeight = 30;
  const nodeWidth = 120;
  const verticalSpacing = 60;
  const horizontalSpacing = 180;

  function renderNode(node, x, y) {
    const nodeGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
    nodeGroup.setAttribute("class", "node");
    nodeGroup.setAttribute("transform", `translate(${x}, ${y})`);

    const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
    rect.setAttribute("width", nodeWidth);
    rect.setAttribute("height", nodeHeight);
    rect.setAttribute("x", 0);
    rect.setAttribute("y", 0);
    nodeGroup.appendChild(rect);

    const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
    text.setAttribute("x", nodeWidth / 2);
    text.setAttribute("y", 20);
    text.setAttribute("text-anchor", "middle");
    text.textContent = node.text;
    nodeGroup.appendChild(text);

    g.appendChild(nodeGroup);

    const childX = x + horizontalSpacing;
    let childY = y;

    node.children.forEach((child) => {
      renderNode(child, childX, childY);

      const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
      line.setAttribute("x1", x + nodeWidth);
      line.setAttribute("y1", y + nodeHeight / 2);
      line.setAttribute("x2", childX);
      line.setAttribute("y2", childY + nodeHeight / 2);
      line.setAttribute("stroke", "black");
      g.insertBefore(line, nodeGroup);

      childY += verticalSpacing;
    });

    return node.children.length * verticalSpacing || verticalSpacing;
  }

  function renderForest(forest, startX = 20, startY = 20) {
    let offsetY = startY;
    forest.forEach((tree) => {
      const treeHeight = renderNode(tree, startX, offsetY);
      offsetY += Math.max(treeHeight, verticalSpacing * 2);
    });
  }

  renderForest(data);

  // Pan & zoom
  let viewBox = { x: 0, y: 0, width: window.innerWidth, height: window.innerHeight };
  svg.setAttribute("viewBox", `${viewBox.x} ${viewBox.y} ${viewBox.width} ${viewBox.height}`);

  let isDragging = false;
  let dragStart = { x: 0, y: 0 };

  svg.addEventListener("mousedown", (e) => {
    isDragging = true;
    dragStart = { x: e.clientX, y: e.clientY };
    svg.style.cursor = "grabbing";
  });

  svg.addEventListener("mousemove", (e) => {
    if (isDragging) {
      const dx = e.clientX - dragStart.x;
      const dy = e.clientY - dragStart.y;
      viewBox.x -= dx;
      viewBox.y -= dy;
      svg.setAttribute("viewBox", `${viewBox.x} ${viewBox.y} ${viewBox.width} ${viewBox.height}`);
      dragStart = { x: e.clientX, y: e.clientY };
    }
  });

  svg.addEventListener("mouseup", () => {
    isDragging = false;
    svg.style.cursor = "grab";
  });

  svg.addEventListener("wheel", (e) => {
    e.preventDefault();
    const zoomFactor = 1.1;
    const scale = e.deltaY < 0 ? 1 / zoomFactor : zoomFactor;
    const mouseX = e.clientX / window.innerWidth * viewBox.width + viewBox.x;
    const mouseY = e.clientY / window.innerHeight * viewBox.height + viewBox.y;
    viewBox.x = mouseX - (mouseX - viewBox.x) * scale;
    viewBox.y = mouseY - (mouseY - viewBox.y) * scale;
    viewBox.width *= scale;
    viewBox.height *= scale;
    svg.setAttribute("viewBox", `${viewBox.x} ${viewBox.y} ${viewBox.width} ${viewBox.height}`);
  });
</script>
</body>
</html>
